/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

// ───────────────────────────────────────────────────
const COLS         = 5;
const ROWS         = 5;
const SYMBOL_SIZE  = 180;  // px of actual symbol in each frame
const GUTTER       = 10;   // px between each frame
const OUTER_MARGIN = 16;   // px around all edges

// Derived:
const CELL_SIZE    = SYMBOL_SIZE + GUTTER;  
const SPRITE_WIDTH  = SYMBOL_SIZE*COLS + GUTTER*(COLS-1) + OUTER_MARGIN*2;
const SPRITE_HEIGHT = SYMBOL_SIZE*ROWS + GUTTER*(ROWS-1) + OUTER_MARGIN*2;
// ───────────────────────────────────────────────────


/**
 * Generate animation prompts based on complexity level - WITH POSITION CONSISTENCY
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle glow pulsing, subtle sparkle effects, soft breathing glow - SYMBOL STAYS IN SAME POSITION',
    medium: 'dynamic glow with rotation effects, pulsing energy aura, color shifts, particle effects around symbol - SYMBOL CORE POSITION NEVER CHANGES',
    complex: 'elaborate magical effects, multiple glow layers, dramatic lighting changes, complex particle systems - MAIN SYMBOL ALWAYS CENTERED AND SAME SIZE'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details - WITH POSITION CONSISTENCY
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing around symbol, symbol radiating energy FROM SAME POSITION',
    'symbol-scatter': 'SCATTER text sparkling around symbol, symbol with magical aura FROM FIXED POSITION',
    'symbol-bonus': 'BONUS text effects around symbol, symbol with celebration effects FROM SAME SPOT',
    'symbol-free': 'FREE text floating around symbol, symbol with liberation effects FROM FIXED POSITION',
    'symbol-jackpot': 'JACKPOT text shimmering around symbol, symbol with golden rays FROM SAME POSITION',
    'symbol-only': 'symbol with natural glow and energy effects FROM CONSISTENT POSITION',
    'text-only': 'text with dynamic typography effects FROM SAME POSITION'
  };
  return (contentAnimations as any)[contentType] || 'symbol with smooth animation FROM FIXED POSITION';
};

/**
 * Generate a 5x5 sprite sheet for animation with position consistency enforcement
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating 5x5 sprite sheet for animation...');

    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);

    // Try multiple generation approaches for better consistency
    let bestResult: SpriteSheetResult | null = null;
    const maxAttempts = 2;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`🎯 Generation attempt ${attempt}/${maxAttempts}`);

      // Create ULTRA-SIMPLIFIED prompt focusing on STATIC OBJECT with effects
      const spriteSheetPrompt = `
Create a 5x5 sprite sheet (25 frames) for slot machine animation.

OBJECT: ${config.prompt}

CRITICAL POSITIONING RULE:
- Draw the SAME object in the EXACT SAME POSITION in all 25 frames
- Think of it like a statue that never moves, only glows/sparkles around it
- Frame 1 = Frame 25 for perfect loop

GRID: 5x5 layout, 1024x1024 total, transparent background

ANIMATION: Only add ${animationDesc} AROUND the object, never move the object itself.

Example: If treasure chest - chest body stays put, only lid opens/gems sparkle.

Style: Professional slot game art, consistent object placement.
`;

      console.log(`🎨 Attempt ${attempt} - Simplified prompt:`, spriteSheetPrompt.substring(0, 150) + '...');

      // Generate the sprite sheet
      const response = await enhancedOpenaiClient.generateImageWithConfig({
        prompt: spriteSheetPrompt,
        targetSymbolId: `spritesheet_${Date.now()}_attempt${attempt}`,
        gameId: 'animation-lab-sprites'
      });

      if (response.success && response.images && response.images.length > 0) {
        const result = {
          success: true,
          spriteSheetUrl: response.images[0]
        };

        console.log(`✅ Attempt ${attempt} generated successfully`);

        // For now, return the first successful result
        // In the future, we could add position consistency validation here
        return result;
      } else {
        console.warn(`⚠️ Attempt ${attempt} failed:`, response.error);
        if (attempt === maxAttempts) {
          throw new Error(response.error || 'All generation attempts failed');
        }
      }
    }

    // This should not be reached due to the loop logic
    throw new Error('Unexpected error in generation loop');

  } catch (error) {
    console.error('❌ Sprite sheet generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Create a position-consistent sprite sheet using a template approach
 */
export const generatePositionConsistentSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎯 Generating position-consistent sprite sheet using template approach...');

    // Step 1: Generate a single reference frame
    const referencePrompt = `
Create a single, perfectly centered ${config.prompt} for a slot machine game.

REQUIREMENTS:
- Single object, perfectly centered
- Transparent background
- High quality, professional game art
- Size: 512x512 pixels
- Object should occupy about 70% of the frame
- Clean, sharp edges
- Vibrant colors suitable for slot games

Style: Professional slot machine symbol art, transparent background, centered composition.
`;

    console.log('🎨 Generating reference frame...');
    const referenceResponse = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: referencePrompt,
      targetSymbolId: `reference_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (!referenceResponse.success || !referenceResponse.images || referenceResponse.images.length === 0) {
      throw new Error('Failed to generate reference frame');
    }

    // Step 2: Generate sprite sheet using the reference
    const spriteSheetPrompt = `
Create a 5x5 sprite sheet (25 frames) based on this reference image, with IDENTICAL positioning:

CRITICAL POSITIONING RULES:
- Use the reference image as a template for EXACT positioning
- Place the same object in the EXACT SAME POSITION in all 25 frames
- Keep the EXACT SAME SIZE across all frames
- Only animate effects like glow, sparkles, opening/closing parts
- Frame 1 and Frame 25 should be nearly identical for seamless looping

GRID SPECIFICATIONS:
- 5x5 grid layout (25 frames total)
- 1024x1024 total image size
- Each frame: approximately 204x204 pixels
- Transparent background
- Clear spacing between frames

ANIMATION PROGRESSION:
- Frames 1-5: Base state with minimal effects
- Frames 6-10: Building energy/glow
- Frames 11-15: Peak animation (maximum effects)
- Frames 16-20: Reducing effects
- Frames 21-25: Return to base state

SYMBOL: ${config.prompt}
ANIMATION TYPE: ${getAnimationDescription(config.animationComplexity)}

Remember: POSITION and SIZE must be IDENTICAL across all frames. Only animate effects, not the object itself.

Style: Professional slot machine game art, transparent background, consistent positioning.
`;

    console.log('🎨 Generating sprite sheet from reference...');
    const spriteResponse = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      sourceImage: referenceResponse.images[0],
      targetSymbolId: `spritesheet_template_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (spriteResponse.success && spriteResponse.images && spriteResponse.images.length > 0) {
      console.log('✅ Position-consistent sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: spriteResponse.images[0]
      };
    } else {
      throw new Error(spriteResponse.error || 'Failed to generate sprite sheet from reference');
    }

  } catch (error) {
    console.error('❌ Position-consistent sprite sheet generation failed:', error);
    // Fallback to original method
    console.log('🔄 Falling back to original generation method...');
    return generateSpriteSheet(config);
  }
};

/**
 * Extract individual frames from a 5x5 sprite sheet
 * This function will be used by the PixiJS animation system
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any, // PIXI.Texture
  frameWidth: number,
  frameHeight: number
): any[] => {
  const frames = [];

  // Extract 25 frames from 5x5 grid
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const frameTexture = new (window as any).PIXI.Texture(
        spriteSheetTexture,
        new (window as any).PIXI.Rectangle(
          col * frameWidth,
          row * frameHeight,
          frameWidth,
          frameHeight
        )
      );
      frames.push(frameTexture);
    }
  }

  return frames;
};
