/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

// ───────────────────────────────────────────────────
const COLS         = 5;
const ROWS         = 5;
const SYMBOL_SIZE  = 180;  // px of actual symbol in each frame
const GUTTER       = 10;   // px between each frame
const OUTER_MARGIN = 16;   // px around all edges

// Derived:
const CELL_SIZE    = SYMBOL_SIZE + GUTTER;  
const SPRITE_WIDTH  = SYMBOL_SIZE*COLS + GUTTER*(COLS-1) + OUTER_MARGIN*2;
const SPRITE_HEIGHT = SYMBOL_SIZE*ROWS + GUTTER*(ROWS-1) + OUTER_MARGIN*2;
// ───────────────────────────────────────────────────


/**
 * Generate animation prompts based on complexity level - WITH POSITION CONSISTENCY
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle glow pulsing, subtle sparkle effects, soft breathing glow - SYMBOL STAYS IN SAME POSITION',
    medium: 'dynamic glow with rotation effects, pulsing energy aura, color shifts, particle effects around symbol - SYMBOL CORE POSITION NEVER CHANGES',
    complex: 'elaborate magical effects, multiple glow layers, dramatic lighting changes, complex particle systems - MAIN SYMBOL ALWAYS CENTERED AND SAME SIZE'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details - WITH POSITION CONSISTENCY
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing around symbol, symbol radiating energy FROM SAME POSITION',
    'symbol-scatter': 'SCATTER text sparkling around symbol, symbol with magical aura FROM FIXED POSITION',
    'symbol-bonus': 'BONUS text effects around symbol, symbol with celebration effects FROM SAME SPOT',
    'symbol-free': 'FREE text floating around symbol, symbol with liberation effects FROM FIXED POSITION',
    'symbol-jackpot': 'JACKPOT text shimmering around symbol, symbol with golden rays FROM SAME POSITION',
    'symbol-only': 'symbol with natural glow and energy effects FROM CONSISTENT POSITION',
    'text-only': 'text with dynamic typography effects FROM SAME POSITION'
  };
  return (contentAnimations as any)[contentType] || 'symbol with smooth animation FROM FIXED POSITION';
};

/**
 * Generate a 5x5 sprite sheet for animation
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating 5x5 sprite sheet for animation...');
    
    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);
    
    // Create enhanced prompt for sprite sheet generation with CRITICAL positioning requirements
    const spriteSheetPrompt = `
Create a PRECISE 5x5 grid sprite sheet (25 frames total) for slot machine symbol animation with ABSOLUTE POSITIONING CONSISTENCY:

🚨 CRITICAL POSITIONING REQUIREMENTS - MOST IMPORTANT:
- The MAIN SYMBOL must be in EXACTLY THE SAME POSITION in ALL 25 frames
- The MAIN SYMBOL must be EXACTLY THE SAME SIZE in ALL 25 frames
- The MAIN SYMBOL must be PERFECTLY CENTERED in each frame
- ONLY animate effects like glow, sparkles, rotation, opening/closing - NEVER move or resize the base symbol
- Frame 1 and Frame 25 must look nearly IDENTICAL for seamless looping
- If it's a treasure chest: keep the chest body in same position, only animate lid opening/gems/glow
- If it's any object: keep the object's base position and size constant, only animate effects around it

SYMBOL DESCRIPTION: ${config.prompt}

ANIMATION RULES FOR CONSISTENT POSITIONING:
- Base symbol position: NEVER CHANGES across all 25 frames
- Base symbol size: NEVER CHANGES across all 25 frames
- Base symbol orientation: Keep consistent (slight rotation OK, but same general angle)
- Only animate: glow effects, particle effects, color changes, opening/closing parts, sparkles
- For treasure chests: chest body stays same position/size, only lid and gems animate
- For gems/crystals: gem stays same position/size, only glow/sparkle effects animate
- For any symbol: core object fixed, only magical effects around it animate

CRITICAL GRID REQUIREMENTS WITH SPACING:
- EXACT 5x5 grid layout with CONSISTENT SPACING between frames
- Add 10-pixel transparent padding between each frame for clean separation
- Each frame content area should be 180x180 pixels with 10px spacing around it
- Total image should be 1024x1024 pixels with proper frame distribution
- Frame layout: 5 columns × 5 rows with equal spacing throughout
- IMPORTANT: Leave 16px margin from all edges to prevent cutting
- Each frame boundary should be clearly defined with transparent spacing

BACKGROUND REQUIREMENTS:
- COMPLETELY TRANSPARENT background for all frames and it's important that the background should be transparent
- NO solid colors, NO gradients in background areas just transparent
- Only the symbol/text should be visible, everything else transparent
- Use PNG format with alpha channel transparency
- Background must be 100% transparent (alpha = 0)

ANIMATION TYPE: ${animationDesc}
CONTENT ANIMATION: ${contentAnimationDesc}

🎯 FRAME PROGRESSION WITH ABSOLUTE POSITION CONSISTENCY:
- ALL FRAMES: Main symbol at EXACT SAME position (center of each frame), EXACT SAME size
- Frames 1-5 (TOP ROW): Starting state - symbol in base position, minimal effects
- Frames 6-10 (SECOND ROW): Building energy - symbol SAME position, add glow/sparkle effects
- Frames 11-15 (MIDDLE ROW): Peak animation - symbol SAME position, maximum effects/opening
- Frames 16-20 (FOURTH ROW): Transitioning back - symbol SAME position, effects reducing
- Frames 21-25 (BOTTOM ROW): Return to start - symbol SAME position, back to minimal effects

🔄 SEAMLESS LOOP REQUIREMENTS:
- Frame 1 and Frame 25 must be nearly IDENTICAL for perfect looping
- Symbol position: EXACTLY the same in frames 1 and 25
- Symbol size: EXACTLY the same in frames 1 and 25
- Only slight effect differences allowed between frame 1 and 25
- Animation must cycle smoothly from frame 25 back to frame 1

📐 POSITIONING REFERENCE GUIDE:
- Draw an invisible reference box in the center of each frame
- Place the main symbol's center point at the center of this reference box
- Keep this center point IDENTICAL across all 25 frames
- Symbol's bounding box should be roughly 70-80% of frame area
- For treasure chest example: chest base always at same spot, only lid/gems animate
- For gem example: gem core always at same spot, only glow/sparkle effects animate

TECHNICAL REQUIREMENTS:
- ${config.symbolType === 'block' ? 'Square symbol format with transparent background' : 'Custom shape with transparent background'}
- ${config.layoutTemplate ? `Text layout: ${config.layoutTemplate}` : 'Integrated text and symbol design'}
- Professional game art quality with clean, sharp edges
- NO background elements, NO environmental effects
- Focus only on the symbol/text animation with CONSISTENT positioning

🚨 FINAL POSITIONING CHECK:
- If you were to overlay all 25 frames, the main symbol outline should match perfectly
- Only the animated effects (glow, sparkles, opening parts) should differ between frames
- The core symbol shape and position must be identical across all frames

💎 TREASURE CHEST SPECIFIC EXAMPLE (if applicable):
- Chest base/body: EXACTLY same position and size in all 25 frames
- Chest lid: Can open/close but chest body stays put
- Gems inside: Can sparkle/glow but chest position never changes
- Frame 1: Chest closed or slightly open, minimal glow
- Frame 25: Should return to nearly same state as Frame 1
- Animation: lid opening → gems glowing → lid closing, but chest base NEVER moves

🎯 POSITION VERIFICATION:
- Draw an imaginary crosshair at center of each frame
- Main symbol's center point must align with this crosshair in ALL frames
- Symbol's width and height must be consistent across ALL frames
- Only surface effects, glow, and small moving parts should animate

Style: Professional slot machine game art, vibrant colors, transparent background, pixel-perfect grid alignment, high quality digital illustration optimized for sprite sheet cutting with ABSOLUTE POSITION CONSISTENCY.
`;

    console.log('🎨 Sprite sheet prompt:', spriteSheetPrompt.substring(0, 200) + '...');

    // Generate the sprite sheet using OpenAI with optimal settings
    const response = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      targetSymbolId: `spritesheet_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (response.success && response.images && response.images.length > 0) {
      console.log('✅ Sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: response.images[0]
      };
    } else {
      throw new Error(response.error || 'Failed to generate sprite sheet');
    }

  } catch (error) {
    console.error('❌ Sprite sheet generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Extract individual frames from a 5x5 sprite sheet
 * This function will be used by the PixiJS animation system
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any, // PIXI.Texture
  frameWidth: number,
  frameHeight: number
): any[] => {
  const frames = [];

  // Extract 25 frames from 5x5 grid
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const frameTexture = new (window as any).PIXI.Texture(
        spriteSheetTexture,
        new (window as any).PIXI.Rectangle(
          col * frameWidth,
          row * frameHeight,
          frameWidth,
          frameHeight
        )
      );
      frames.push(frameTexture);
    }
  }

  return frames;
};
